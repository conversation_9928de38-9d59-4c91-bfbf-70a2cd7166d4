FROM --platform=linux/amd64 python:{{cookiecutter.python_version}}-alpine3.20
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1
ENV PYTHONBREAKPOINT ipdb.set_trace
ENV {{cookiecutter.project_slug.upper()}}_ENV dev
RUN apk update
RUN apk add --no-cache git libmagic openssh-client
RUN mkdir -p -m 0700 ~/.ssh && ssh-keyscan github.com >> ~/.ssh/known_hosts
RUN git config --global --add safe.directory /usr/src/{{cookiecutter.project_slug}}
WORKDIR /usr/src/{{cookiecutter.project_slug}}/backend/django
RUN python -m ensurepip --upgrade
ENTRYPOINT ["/usr/src/{{cookiecutter.project_slug}}/docker/dev/{{cookiecutter.project_slug}}_backend-entrypoint.sh"]
